"""
Widget de rapports modernisé avec génération intelligente et export avancé
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QFrame, QScrollArea, QTabWidget, QComboBox, QPushButton,
    QDateEdit, QCheckBox, QButtonGroup, QRadioButton, QSpinBox,
    QProgressBar, QGroupBox, QSplitter, QListWidget, QListWidgetItem,
    QTextEdit, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json


class ReportGenerationThread(QThread):
    """Thread pour la génération de rapports en arrière-plan"""
    
    progress_updated = pyqtSignal(int)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, report_config: Dict):
        super().__init__()
        self.report_config = report_config
        
    def run(self):
        """Génère le rapport en arrière-plan"""
        try:
            # Simulation de génération de rapport
            for i in range(101):
                self.progress_updated.emit(i)
                self.msleep(20)  # Simulation du temps de traitement
                
            # Données de rapport simulées
            report_data = {
                "title": self.report_config.get("title", "Rapport"),
                "period": self.report_config.get("period", "Mensuel"),
                "generated_at": datetime.now().isoformat(),
                "data": {
                    "total_repairs": 156,
                    "total_revenue": 45230.50,
                    "avg_repair_time": 2.5,
                    "customer_satisfaction": 4.6
                }
            }
            
            self.report_generated.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class ModernReportCard(QFrame):
    """Carte de rapport moderne"""
    
    generate_requested = pyqtSignal(dict)
    
    def __init__(self, title: str, description: str, report_type: str, icon: str = "📊"):
        super().__init__()
        self.title = title
        self.description = description
        self.report_type = report_type
        self.icon = icon
        self.setup_ui()
        
    def setup_ui(self):
        self.setObjectName("reportCard")
        self.setFixedHeight(180)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # En-tête avec icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setObjectName("reportIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setObjectName("reportTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(self.description)
        desc_label.setObjectName("reportDescription")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        layout.addStretch()
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        generate_button = QPushButton("Générer")
        generate_button.setObjectName("generateButton")
        generate_button.clicked.connect(self.request_generation)
        actions_layout.addWidget(generate_button)
        
        schedule_button = QPushButton("Programmer")
        schedule_button.setObjectName("scheduleButton")
        actions_layout.addWidget(schedule_button)
        
        layout.addLayout(actions_layout)
        
        self.apply_styles()
        
    def apply_styles(self):
        """Applique les styles à la carte"""
        self.setStyleSheet("""
            #reportCard {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 12px;
                margin: 10px;
            }
            #reportCard:hover {
                border: 2px solid #2196F3;
                box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
            }
            #reportIcon {
                font-size: 24px;
                margin-right: 10px;
            }
            #reportTitle {
                color: #212121;
                font-size: 16px;
                font-weight: bold;
            }
            #reportDescription {
                color: #757575;
                font-size: 14px;
                line-height: 1.4;
            }
            #generateButton {
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
            }
            #generateButton:hover {
                background: #1976D2;
            }
            #scheduleButton {
                background: #F5F5F5;
                color: #757575;
                border: 1px solid #E0E0E0;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
            }
            #scheduleButton:hover {
                background: #E0E0E0;
                color: #212121;
            }
        """)
        
    def request_generation(self):
        """Demande la génération du rapport"""
        config = {
            "title": self.title,
            "type": self.report_type,
            "description": self.description
        }
        self.generate_requested.emit(config)


class ModernReportsWidget(QWidget):
    """Widget principal de rapports modernisé"""
    
    def __init__(self):
        super().__init__()
        self.generation_thread = None
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec contrôles
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Génération de Rapports")
        title_label.setObjectName("sectionTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Boutons d'action globaux
        templates_button = QPushButton("📋 Modèles")
        templates_button.setObjectName("actionButton")
        header_layout.addWidget(templates_button)
        
        history_button = QPushButton("📚 Historique")
        history_button.setObjectName("actionButton")
        header_layout.addWidget(history_button)
        
        settings_button = QPushButton("⚙️ Paramètres")
        settings_button.setObjectName("actionButton")
        header_layout.addWidget(settings_button)
        
        layout.addLayout(header_layout)
        
        # Onglets de rapports
        self.reports_tabs = QTabWidget()
        self.reports_tabs.setObjectName("reportsTabs")
        
        # Onglet Rapports Prédéfinis
        predefined_tab = self.create_predefined_reports_tab()
        self.reports_tabs.addTab(predefined_tab, "📊 Rapports Prédéfinis")
        
        # Onglet Rapports Personnalisés
        custom_tab = self.create_custom_reports_tab()
        self.reports_tabs.addTab(custom_tab, "🎯 Rapports Personnalisés")
        
        # Onglet Rapports Automatiques
        automated_tab = self.create_automated_reports_tab()
        self.reports_tabs.addTab(automated_tab, "🤖 Rapports Automatiques")
        
        # Onglet Historique et Export
        export_tab = self.create_export_tab()
        self.reports_tabs.addTab(export_tab, "📤 Export & Historique")
        
        layout.addWidget(self.reports_tabs)
        
        # Barre de progression pour la génération
        self.progress_frame = QFrame()
        self.progress_frame.setObjectName("progressFrame")
        self.progress_frame.setVisible(False)
        
        progress_layout = QVBoxLayout(self.progress_frame)
        
        self.progress_label = QLabel("Génération en cours...")
        self.progress_label.setObjectName("progressLabel")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(self.progress_frame)
        
        self.apply_styles()
        
    def create_predefined_reports_tab(self):
        """Crée l'onglet des rapports prédéfinis"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Description
        desc_label = QLabel("Sélectionnez un rapport prédéfini à générer:")
        desc_label.setObjectName("tabDescription")
        layout.addWidget(desc_label)
        
        # Grille de cartes de rapports
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        
        # Rapports prédéfinis
        predefined_reports = [
            ("Rapport Mensuel", "Analyse complète des activités du mois", "monthly", "📅"),
            ("Rapport de Performance", "Performance des techniciens et équipes", "performance", "🏆"),
            ("Rapport Financier", "Analyse des revenus et dépenses", "financial", "💰"),
            ("Rapport d'Inventaire", "État des stocks et mouvements", "inventory", "📦"),
            ("Rapport Client", "Satisfaction et analyses clients", "customer", "👥"),
            ("Rapport Technique", "Analyses techniques et réparations", "technical", "🔧"),
            ("Rapport Exécutif", "Vue d'ensemble pour la direction", "executive", "📈"),
            ("Rapport Qualité", "Contrôle qualité et conformité", "quality", "✅")
        ]
        
        self.report_cards = {}
        for i, (title, desc, report_type, icon) in enumerate(predefined_reports):
            card = ModernReportCard(title, desc, report_type, icon)
            card.generate_requested.connect(self.generate_report)
            self.report_cards[report_type] = card
            
            row, col = divmod(i, 2)
            scroll_layout.addWidget(card, row, col)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return tab
        
    def create_custom_reports_tab(self):
        """Crée l'onglet des rapports personnalisés"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Constructeur de rapport
        builder_frame = QFrame()
        builder_frame.setObjectName("builderFrame")
        builder_layout = QGridLayout(builder_frame)
        
        # Configuration du rapport
        builder_layout.addWidget(QLabel("Nom du rapport:"), 0, 0)
        self.custom_name_edit = QTextEdit()
        self.custom_name_edit.setMaximumHeight(30)
        builder_layout.addWidget(self.custom_name_edit, 0, 1, 1, 2)
        
        builder_layout.addWidget(QLabel("Type de données:"), 1, 0)
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems([
            "Réparations", "Ventes", "Achats", "Inventaire", 
            "Clients", "Techniciens", "Équipements"
        ])
        builder_layout.addWidget(self.data_type_combo, 1, 1)
        
        builder_layout.addWidget(QLabel("Période:"), 1, 2)
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "Dernière semaine", "Dernier mois", "Dernier trimestre",
            "Dernière année", "Période personnalisée"
        ])
        builder_layout.addWidget(self.period_combo, 1, 3)
        
        builder_layout.addWidget(QLabel("Format de sortie:"), 2, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["PDF", "Excel", "CSV", "JSON"])
        builder_layout.addWidget(self.format_combo, 2, 1)
        
        # Bouton de génération
        generate_custom_button = QPushButton("Générer Rapport Personnalisé")
        generate_custom_button.setObjectName("generateButton")
        generate_custom_button.clicked.connect(self.generate_custom_report)
        builder_layout.addWidget(generate_custom_button, 2, 2, 1, 2)
        
        layout.addWidget(builder_frame)
        
        # Aperçu des données
        preview_frame = QFrame()
        preview_frame.setObjectName("previewFrame")
        preview_layout = QVBoxLayout(preview_frame)
        
        preview_label = QLabel("Aperçu des données:")
        preview_label.setObjectName("previewLabel")
        preview_layout.addWidget(preview_label)
        
        self.preview_text = QTextEdit()
        self.preview_text.setObjectName("previewText")
        self.preview_text.setPlainText("Sélectionnez les paramètres ci-dessus pour voir un aperçu des données...")
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_frame)
        
        return tab
        
    def create_automated_reports_tab(self):
        """Crée l'onglet des rapports automatiques"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuration des rapports automatiques
        auto_frame = QFrame()
        auto_frame.setObjectName("autoFrame")
        auto_layout = QVBoxLayout(auto_frame)
        
        auto_label = QLabel("🤖 Configuration des Rapports Automatiques")
        auto_label.setObjectName("autoTitle")
        auto_layout.addWidget(auto_label)
        
        # Liste des rapports programmés
        scheduled_list = QListWidget()
        scheduled_list.setObjectName("scheduledList")
        
        # Ajouter quelques exemples
        for report in ["Rapport Mensuel - Chaque 1er du mois", "Rapport Hebdomadaire - Chaque lundi", "Rapport Financier - Chaque trimestre"]:
            item = QListWidgetItem(report)
            scheduled_list.addItem(item)
        
        auto_layout.addWidget(scheduled_list)
        
        # Boutons de gestion
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("➕ Ajouter")
        add_button.setObjectName("actionButton")
        buttons_layout.addWidget(add_button)
        
        edit_button = QPushButton("✏️ Modifier")
        edit_button.setObjectName("actionButton")
        buttons_layout.addWidget(edit_button)
        
        delete_button = QPushButton("🗑️ Supprimer")
        delete_button.setObjectName("actionButton")
        buttons_layout.addWidget(delete_button)
        
        buttons_layout.addStretch()
        
        auto_layout.addLayout(buttons_layout)
        
        layout.addWidget(auto_frame)
        
        return tab
        
    def create_export_tab(self):
        """Crée l'onglet d'export et historique"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Historique des rapports
        history_frame = QFrame()
        history_frame.setObjectName("historyFrame")
        history_layout = QVBoxLayout(history_frame)
        
        history_label = QLabel("📚 Historique des Rapports Générés")
        history_label.setObjectName("historyTitle")
        history_layout.addWidget(history_label)
        
        # Liste de l'historique
        self.history_list = QListWidget()
        self.history_list.setObjectName("historyList")
        
        # Ajouter quelques exemples
        history_items = [
            "Rapport Mensuel - Janvier 2024 (PDF) - 15/01/2024",
            "Rapport Performance - Q4 2023 (Excel) - 10/01/2024",
            "Rapport Financier - Décembre 2023 (PDF) - 05/01/2024"
        ]
        
        for item_text in history_items:
            item = QListWidgetItem(item_text)
            self.history_list.addItem(item)
        
        history_layout.addWidget(self.history_list)
        
        # Boutons d'action pour l'historique
        history_buttons_layout = QHBoxLayout()
        
        open_button = QPushButton("📂 Ouvrir")
        open_button.setObjectName("actionButton")
        history_buttons_layout.addWidget(open_button)
        
        export_button = QPushButton("📤 Exporter")
        export_button.setObjectName("actionButton")
        history_buttons_layout.addWidget(export_button)
        
        delete_history_button = QPushButton("🗑️ Supprimer")
        delete_history_button.setObjectName("actionButton")
        history_buttons_layout.addWidget(delete_history_button)
        
        history_buttons_layout.addStretch()
        
        history_layout.addLayout(history_buttons_layout)
        
        layout.addWidget(history_frame)
        
        return tab
        
    def apply_styles(self):
        """Applique les styles globaux"""
        self.setStyleSheet("""
            #sectionTitle {
                color: #212121;
                font-size: 24px;
                font-weight: bold;
            }
            #reportsTabs {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }
            #reportsTabs::pane {
                border: none;
                background: white;
                padding: 20px;
            }
            #reportsTabs QTabBar::tab {
                background: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: 500;
            }
            #reportsTabs QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #2196F3;
                color: #2196F3;
                font-weight: bold;
            }
            #tabDescription {
                color: #757575;
                font-size: 14px;
                margin-bottom: 15px;
            }
            #builderFrame, #previewFrame, #autoFrame, #historyFrame {
                background: #FAFAFA;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px 0;
            }
            #progressFrame {
                background: #E3F2FD;
                border: 1px solid #2196F3;
                border-radius: 8px;
                padding: 15px;
            }
            #progressLabel {
                color: #1976D2;
                font-weight: bold;
                margin-bottom: 10px;
            }
            #progressBar {
                border: none;
                background: rgba(255,255,255,0.5);
                border-radius: 4px;
                height: 8px;
            }
            #progressBar::chunk {
                background: #2196F3;
                border-radius: 4px;
            }
        """)
        
    def generate_report(self, config: Dict):
        """Génère un rapport avec la configuration donnée"""
        self.progress_frame.setVisible(True)
        self.progress_label.setText(f"Génération du {config['title']}...")
        self.progress_bar.setValue(0)
        
        # Lancer la génération en arrière-plan
        self.generation_thread = ReportGenerationThread(config)
        self.generation_thread.progress_updated.connect(self.progress_bar.setValue)
        self.generation_thread.report_generated.connect(self.on_report_generated)
        self.generation_thread.error_occurred.connect(self.on_generation_error)
        self.generation_thread.start()
        
    def generate_custom_report(self):
        """Génère un rapport personnalisé"""
        config = {
            "title": self.custom_name_edit.toPlainText() or "Rapport Personnalisé",
            "type": "custom",
            "data_type": self.data_type_combo.currentText(),
            "period": self.period_combo.currentText(),
            "format": self.format_combo.currentText()
        }
        self.generate_report(config)
        
    def on_report_generated(self, report_data: Dict):
        """Callback quand un rapport est généré"""
        self.progress_frame.setVisible(False)
        
        # Ajouter à l'historique
        timestamp = datetime.now().strftime("%d/%m/%Y %H:%M")
        history_text = f"{report_data['title']} - {timestamp}"
        item = QListWidgetItem(history_text)
        self.history_list.insertItem(0, item)
        
        # Afficher un message de succès
        QMessageBox.information(
            self,
            "Rapport Généré",
            f"Le rapport '{report_data['title']}' a été généré avec succès!"
        )
        
    def on_generation_error(self, error_message: str):
        """Callback en cas d'erreur de génération"""
        self.progress_frame.setVisible(False)
        QMessageBox.critical(
            self,
            "Erreur de Génération",
            f"Une erreur s'est produite lors de la génération du rapport:\n{error_message}"
        )
