"""
Widget d'opérations modernisé pour le suivi des activités en temps réel
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QFrame, QScrollArea, QTabWidget, QComboBox, QPushButton,
    QListWidget, QListWidgetItem, QProgressBar, QGroupBox,
    QSplitter, QTableWidget, QTableWidgetItem, QHeaderView,
    QCheckBox, QSpinBox, QDateTimeEdit, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, QDateTime, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import random


class OperationStatusCard(QFrame):
    """Carte de statut d'opération"""
    
    def __init__(self, title: str, status: str, progress: int = 0, color: str = "#2196F3"):
        super().__init__()
        self.title = title
        self.status = status
        self.progress = progress
        self.color = color
        self.setup_ui()
        
    def setup_ui(self):
        self.setObjectName("operationCard")
        self.setFixedHeight(120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel(self.title)
        title_label.setObjectName("operationTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Indicateur de statut
        status_label = QLabel(self.status)
        status_label.setObjectName("operationStatus")
        header_layout.addWidget(status_label)
        
        layout.addLayout(header_layout)
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("operationProgress")
        self.progress_bar.setValue(self.progress)
        layout.addWidget(self.progress_bar)
        
        # Informations supplémentaires
        info_layout = QHBoxLayout()
        
        self.time_label = QLabel("Démarré: 10:30")
        self.time_label.setObjectName("operationTime")
        info_layout.addWidget(self.time_label)
        
        info_layout.addStretch()
        
        self.eta_label = QLabel("ETA: 15 min")
        self.eta_label.setObjectName("operationETA")
        info_layout.addWidget(self.eta_label)
        
        layout.addLayout(info_layout)
        
        self.apply_styles()
        
    def apply_styles(self):
        """Applique les styles personnalisés"""
        status_colors = {
            "En cours": "#FF9800",
            "Terminé": "#4CAF50",
            "En attente": "#757575",
            "Erreur": "#F44336"
        }
        
        status_color = status_colors.get(self.status, self.color)
        
        self.setStyleSheet(f"""
            #operationCard {{
                background: white;
                border: 1px solid #E0E0E0;
                border-left: 4px solid {status_color};
                border-radius: 8px;
                margin: 5px;
            }}
            #operationCard:hover {{
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            #operationTitle {{
                color: #212121;
                font-size: 14px;
                font-weight: bold;
            }}
            #operationStatus {{
                color: {status_color};
                font-size: 12px;
                font-weight: 500;
                background: {status_color}20;
                padding: 4px 8px;
                border-radius: 12px;
            }}
            #operationProgress {{
                border: none;
                background: #F5F5F5;
                border-radius: 4px;
                height: 8px;
            }}
            #operationProgress::chunk {{
                background: {status_color};
                border-radius: 4px;
            }}
            #operationTime, #operationETA {{
                color: #757575;
                font-size: 11px;
            }}
        """)
        
    def update_progress(self, progress: int, status: str = None):
        """Met à jour le progrès et le statut"""
        self.progress = progress
        self.progress_bar.setValue(progress)
        
        if status:
            self.status = status
            self.apply_styles()


class ActivityLogWidget(QWidget):
    """Widget de journal d'activité en temps réel"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📋 Journal d'Activité")
        title_label.setObjectName("logTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Filtres
        filter_combo = QComboBox()
        filter_combo.addItems(["Toutes", "Réparations", "Ventes", "Inventaire", "Système"])
        filter_combo.setObjectName("logFilter")
        header_layout.addWidget(filter_combo)
        
        clear_button = QPushButton("🗑️ Effacer")
        clear_button.setObjectName("logClearButton")
        clear_button.clicked.connect(self.clear_log)
        header_layout.addWidget(clear_button)
        
        layout.addLayout(header_layout)
        
        # Liste des activités
        self.activity_list = QListWidget()
        self.activity_list.setObjectName("activityList")
        layout.addWidget(self.activity_list)
        
        self.apply_styles()
        
    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            #logTitle {
                color: #212121;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            #logFilter {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 100px;
            }
            #logClearButton {
                background: #F44336;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: 500;
            }
            #logClearButton:hover {
                background: #D32F2F;
            }
            #activityList {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 5px;
            }
            #activityList::item {
                padding: 8px;
                border-bottom: 1px solid #F5F5F5;
                border-radius: 4px;
                margin: 2px 0;
            }
            #activityList::item:hover {
                background: #F5F5F5;
            }
        """)
        
    def setup_timer(self):
        """Configure le timer pour les mises à jour automatiques"""
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.add_random_activity)
        self.update_timer.start(5000)  # Nouvelle activité toutes les 5 secondes
        
    def add_activity(self, message: str, activity_type: str = "info"):
        """Ajoute une nouvelle activité au journal"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Icônes par type
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "repair": "🔧",
            "sale": "💰",
            "inventory": "📦"
        }
        
        icon = icons.get(activity_type, "ℹ️")
        full_message = f"{icon} {timestamp} - {message}"
        
        item = QListWidgetItem(full_message)
        self.activity_list.insertItem(0, item)
        
        # Limiter à 100 éléments
        if self.activity_list.count() > 100:
            self.activity_list.takeItem(self.activity_list.count() - 1)
            
    def add_random_activity(self):
        """Ajoute une activité aléatoire pour la démonstration"""
        activities = [
            ("Nouvelle réparation créée: iPhone 12", "repair"),
            ("Vente terminée: 250 DA", "sale"),
            ("Stock mis à jour: Écran Samsung", "inventory"),
            ("Technicien connecté: Ahmed", "info"),
            ("Sauvegarde automatique effectuée", "success"),
            ("Alerte stock faible: Batteries", "warning")
        ]
        
        message, activity_type = random.choice(activities)
        self.add_activity(message, activity_type)
        
    def clear_log(self):
        """Efface le journal d'activité"""
        self.activity_list.clear()


class ModernOperationsWidget(QWidget):
    """Widget principal d'opérations modernisé"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Centre d'Opérations")
        title_label.setObjectName("sectionTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Indicateurs de statut système
        system_status_layout = QHBoxLayout()
        
        self.system_indicators = {}
        indicators = [
            ("database", "Base de Données", "Connectée", "#4CAF50"),
            ("backup", "Sauvegarde", "OK", "#4CAF50"),
            ("sync", "Synchronisation", "En cours", "#FF9800")
        ]
        
        for key, name, status, color in indicators:
            indicator = self.create_system_indicator(name, status, color)
            self.system_indicators[key] = indicator
            system_status_layout.addWidget(indicator)
        
        header_layout.addLayout(system_status_layout)
        
        layout.addLayout(header_layout)
        
        # Splitter principal
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Panneau gauche - Opérations en cours
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Titre des opérations
        ops_title = QLabel("🔄 Opérations en Cours")
        ops_title.setObjectName("panelTitle")
        left_layout.addWidget(ops_title)
        
        # Scroll area pour les cartes d'opération
        ops_scroll = QScrollArea()
        ops_widget = QWidget()
        self.ops_layout = QVBoxLayout(ops_widget)
        
        # Créer quelques opérations d'exemple
        self.operation_cards = []
        self.create_sample_operations()
        
        ops_scroll.setWidget(ops_widget)
        ops_scroll.setWidgetResizable(True)
        left_layout.addWidget(ops_scroll)
        
        main_splitter.addWidget(left_panel)
        
        # Panneau droit - Monitoring et logs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Onglets de monitoring
        monitoring_tabs = QTabWidget()
        monitoring_tabs.setObjectName("monitoringTabs")
        
        # Onglet Journal d'activité
        self.activity_log = ActivityLogWidget()
        monitoring_tabs.addTab(self.activity_log, "📋 Activité")
        
        # Onglet Performances
        performance_tab = self.create_performance_tab()
        monitoring_tabs.addTab(performance_tab, "📊 Performances")
        
        # Onglet Alertes
        alerts_tab = self.create_alerts_tab()
        monitoring_tabs.addTab(alerts_tab, "🔔 Alertes")
        
        right_layout.addWidget(monitoring_tabs)
        
        main_splitter.addWidget(right_panel)
        
        # Proportions du splitter
        main_splitter.setSizes([400, 600])
        
        layout.addWidget(main_splitter)
        
        self.apply_styles()
        
    def create_system_indicator(self, name: str, status: str, color: str) -> QWidget:
        """Crée un indicateur de statut système"""
        widget = QFrame()
        widget.setObjectName("systemIndicator")
        
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Point de statut
        status_dot = QLabel("●")
        status_dot.setStyleSheet(f"color: {color}; font-size: 16px;")
        layout.addWidget(status_dot)
        
        # Nom et statut
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(name)
        name_label.setObjectName("indicatorName")
        info_layout.addWidget(name_label)
        
        status_label = QLabel(status)
        status_label.setObjectName("indicatorStatus")
        info_layout.addWidget(status_label)
        
        layout.addLayout(info_layout)
        
        widget.setStyleSheet(f"""
            #systemIndicator {{
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 0 5px;
            }}
            #indicatorName {{
                color: #212121;
                font-size: 12px;
                font-weight: bold;
            }}
            #indicatorStatus {{
                color: {color};
                font-size: 11px;
            }}
        """)
        
        return widget
        
    def create_sample_operations(self):
        """Crée des opérations d'exemple"""
        operations = [
            ("Sauvegarde automatique", "En cours", 75, "#2196F3"),
            ("Synchronisation données", "En cours", 45, "#FF9800"),
            ("Génération rapport mensuel", "En attente", 0, "#757575"),
            ("Mise à jour inventaire", "Terminé", 100, "#4CAF50"),
            ("Export données client", "En cours", 30, "#9C27B0")
        ]
        
        for title, status, progress, color in operations:
            card = OperationStatusCard(title, status, progress, color)
            self.operation_cards.append(card)
            self.ops_layout.addWidget(card)
            
        self.ops_layout.addStretch()
        
    def create_performance_tab(self):
        """Crée l'onglet de performances"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Métriques de performance
        metrics_frame = QFrame()
        metrics_frame.setObjectName("metricsFrame")
        metrics_layout = QGridLayout(metrics_frame)
        
        metrics = [
            ("CPU", "45%", "#2196F3"),
            ("Mémoire", "62%", "#FF9800"),
            ("Disque", "78%", "#F44336"),
            ("Réseau", "23%", "#4CAF50")
        ]
        
        for i, (name, value, color) in enumerate(metrics):
            metric_widget = self.create_metric_widget(name, value, color)
            row, col = divmod(i, 2)
            metrics_layout.addWidget(metric_widget, row, col)
        
        layout.addWidget(metrics_frame)
        
        # Graphique de performance (placeholder)
        perf_label = QLabel("📊 Graphique de performance en temps réel")
        perf_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        perf_label.setStyleSheet("color: #757575; font-size: 14px; padding: 40px;")
        layout.addWidget(perf_label)
        
        return tab
        
    def create_metric_widget(self, name: str, value: str, color: str) -> QWidget:
        """Crée un widget de métrique"""
        widget = QFrame()
        widget.setObjectName("metricWidget")
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(15, 10, 15, 10)
        
        name_label = QLabel(name)
        name_label.setObjectName("metricName")
        layout.addWidget(name_label)
        
        value_label = QLabel(value)
        value_label.setObjectName("metricValue")
        value_label.setStyleSheet(f"color: {color}; font-size: 20px; font-weight: bold;")
        layout.addWidget(value_label)
        
        # Barre de progression
        progress = QProgressBar()
        progress.setValue(int(value.replace('%', '')))
        progress.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                background: #F5F5F5;
                border-radius: 4px;
                height: 8px;
            }}
            QProgressBar::chunk {{
                background: {color};
                border-radius: 4px;
            }}
        """)
        layout.addWidget(progress)
        
        widget.setStyleSheet("""
            #metricWidget {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                margin: 5px;
            }
            #metricName {
                color: #757575;
                font-size: 12px;
                font-weight: 500;
            }
        """)
        
        return widget
        
    def create_alerts_tab(self):
        """Crée l'onglet d'alertes"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Liste des alertes
        alerts_list = QListWidget()
        alerts_list.setObjectName("alertsList")
        
        # Alertes d'exemple
        alerts = [
            ("⚠️ Stock faible: Batteries iPhone", "warning"),
            ("❌ Échec de sauvegarde automatique", "error"),
            ("ℹ️ Maintenance programmée dans 2h", "info"),
            ("✅ Mise à jour système terminée", "success")
        ]
        
        for alert_text, alert_type in alerts:
            item = QListWidgetItem(alert_text)
            alerts_list.addItem(item)
        
        layout.addWidget(alerts_list)
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        mark_read_button = QPushButton("✓ Marquer comme lu")
        mark_read_button.setObjectName("alertButton")
        actions_layout.addWidget(mark_read_button)
        
        clear_all_button = QPushButton("🗑️ Effacer tout")
        clear_all_button.setObjectName("alertButton")
        actions_layout.addWidget(clear_all_button)
        
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        return tab
        
    def apply_styles(self):
        """Applique les styles globaux"""
        self.setStyleSheet("""
            #sectionTitle {
                color: #212121;
                font-size: 24px;
                font-weight: bold;
            }
            #panelTitle {
                color: #212121;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            #monitoringTabs {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }
            #monitoringTabs::pane {
                border: none;
                background: white;
                padding: 15px;
            }
            #monitoringTabs QTabBar::tab {
                background: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 10px 15px;
                margin-right: 2px;
                font-weight: 500;
            }
            #monitoringTabs QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #2196F3;
                color: #2196F3;
                font-weight: bold;
            }
            #metricsFrame {
                background: #FAFAFA;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
            #alertsList {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 5px;
            }
            #alertButton {
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
            }
            #alertButton:hover {
                background: #1976D2;
            }
        """)
        
    def load_sample_data(self):
        """Charge des données d'exemple"""
        # Ajouter quelques activités initiales
        initial_activities = [
            ("Application démarrée", "success"),
            ("Base de données connectée", "success"),
            ("Chargement des modules terminé", "info")
        ]
        
        for message, activity_type in initial_activities:
            self.activity_log.add_activity(message, activity_type)
            
        # Démarrer la simulation de mise à jour des opérations
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_operations)
        self.update_timer.start(3000)  # Mise à jour toutes les 3 secondes
        
    def update_operations(self):
        """Met à jour les opérations en cours"""
        for card in self.operation_cards:
            if card.status == "En cours":
                new_progress = min(card.progress + random.randint(5, 15), 100)
                new_status = "Terminé" if new_progress >= 100 else "En cours"
                card.update_progress(new_progress, new_status)
