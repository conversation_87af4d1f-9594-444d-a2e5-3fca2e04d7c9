"""
Widget d'analyses modernes avec visualisations interactives
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QFrame, QScrollArea, QTabWidget, QComboBox, QPushButton,
    QSlider, QCheckBox, QButtonGroup, QRadioButton, QSpinBox,
    QProgressBar, QGroupBox, QSplitter
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap, QPainter
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple


class ModernMetricCard(QFrame):
    """Carte de métrique moderne avec animations"""
    
    def __init__(self, title: str, value: str, trend: float = 0, color: str = "#2196F3"):
        super().__init__()
        self.title = title
        self.value = value
        self.trend = trend
        self.color = color
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        self.setObjectName("metricCard")
        self.setFixedHeight(140)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # En-tête avec titre
        header_layout = QHBoxLayout()
        
        title_label = QLabel(self.title)
        title_label.setObjectName("metricTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Icône de tendance
        self.trend_icon = QLabel("📈" if self.trend >= 0 else "📉")
        self.trend_icon.setObjectName("trendIcon")
        header_layout.addWidget(self.trend_icon)
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        self.value_label = QLabel(self.value)
        self.value_label.setObjectName("metricValue")
        layout.addWidget(self.value_label)
        
        # Tendance
        trend_text = f"+{self.trend:.1f}%" if self.trend >= 0 else f"{self.trend:.1f}%"
        self.trend_label = QLabel(trend_text)
        self.trend_label.setObjectName("metricTrend")
        layout.addWidget(self.trend_label)
        
        # Barre de progression (optionnelle)
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("metricProgress")
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.apply_styles()
        
    def apply_styles(self):
        """Applique les styles personnalisés"""
        trend_color = "#4CAF50" if self.trend >= 0 else "#F44336"
        
        self.setStyleSheet(f"""
            #metricCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}15, stop:1 {self.color}08);
                border: 2px solid {self.color}30;
                border-radius: 12px;
                margin: 5px;
            }}
            #metricCard:hover {{
                border: 2px solid {self.color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}25, stop:1 {self.color}15);
            }}
            #metricTitle {{
                color: #757575;
                font-size: 14px;
                font-weight: 500;
            }}
            #metricValue {{
                color: {self.color};
                font-size: 28px;
                font-weight: bold;
            }}
            #metricTrend {{
                color: {trend_color};
                font-size: 12px;
                font-weight: 500;
            }}
            #trendIcon {{
                font-size: 16px;
            }}
            #metricProgress {{
                border: none;
                background: rgba(0,0,0,0.1);
                border-radius: 3px;
                height: 6px;
            }}
            #metricProgress::chunk {{
                background: {self.color};
                border-radius: 3px;
            }}
        """)
        
    def setup_animations(self):
        """Configure les animations"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def update_value(self, new_value: str, new_trend: float = None):
        """Met à jour la valeur et la tendance"""
        self.value = new_value
        self.value_label.setText(new_value)
        
        if new_trend is not None:
            self.trend = new_trend
            trend_text = f"+{self.trend:.1f}%" if self.trend >= 0 else f"{self.trend:.1f}%"
            self.trend_label.setText(trend_text)
            self.trend_icon.setText("📈" if self.trend >= 0 else "📉")
            self.apply_styles()
            
    def set_progress(self, value: int, visible: bool = True):
        """Configure la barre de progression"""
        self.progress_bar.setValue(value)
        self.progress_bar.setVisible(visible)


class ModernChartWidget(QWidget):
    """Widget de graphique moderne avec contrôles interactifs"""
    
    def __init__(self, title: str = "Graphique"):
        super().__init__()
        self.title = title
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête avec contrôles
        header_frame = QFrame()
        header_frame.setObjectName("chartHeader")
        header_layout = QHBoxLayout(header_frame)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setObjectName("chartTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Contrôles
        self.period_combo = QComboBox()
        self.period_combo.addItems(["7 jours", "30 jours", "90 jours", "1 an"])
        self.period_combo.setObjectName("chartControl")
        header_layout.addWidget(self.period_combo)
        
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Ligne", "Barres", "Aires"])
        self.chart_type_combo.setObjectName("chartControl")
        header_layout.addWidget(self.chart_type_combo)
        
        layout.addWidget(header_frame)
        
        # Zone de graphique
        self.figure = Figure(figsize=(10, 6), facecolor='white')
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setObjectName("chartCanvas")
        layout.addWidget(self.canvas)
        
        self.apply_styles()
        
    def apply_styles(self):
        """Applique les styles au widget"""
        self.setStyleSheet("""
            #chartHeader {
                background: #FAFAFA;
                border-bottom: 1px solid #E0E0E0;
                padding: 10px 15px;
            }
            #chartTitle {
                color: #212121;
                font-size: 16px;
                font-weight: bold;
            }
            #chartControl {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 5px 10px;
                margin-left: 10px;
                min-width: 80px;
            }
            #chartCanvas {
                background: white;
                border: none;
            }
        """)
        
    def create_sample_chart(self):
        """Crée un graphique d'exemple"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # Données d'exemple
        x = np.linspace(0, 30, 30)
        y = np.random.normal(100, 15, 30).cumsum()
        
        ax.plot(x, y, color='#2196F3', linewidth=2, marker='o', markersize=4)
        ax.fill_between(x, y, alpha=0.3, color='#2196F3')
        
        ax.set_title(self.title, fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Jours', fontsize=12)
        ax.set_ylabel('Valeur', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        self.figure.tight_layout()
        self.canvas.draw()


class ModernAnalyticsWidget(QWidget):
    """Widget principal d'analyses modernes"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec contrôles globaux
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Analyses Avancées")
        title_label.setObjectName("sectionTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Contrôles de période
        period_label = QLabel("Période d'analyse:")
        header_layout.addWidget(period_label)
        
        self.global_period_combo = QComboBox()
        self.global_period_combo.addItems([
            "Dernières 24h", "7 derniers jours", "30 derniers jours",
            "3 derniers mois", "6 derniers mois", "1 an"
        ])
        self.global_period_combo.setCurrentText("30 derniers jours")
        header_layout.addWidget(self.global_period_combo)
        
        # Bouton de rafraîchissement
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("actionButton")
        refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_button)
        
        layout.addLayout(header_layout)
        
        # Grille de métriques principales
        metrics_frame = QFrame()
        metrics_frame.setObjectName("metricsFrame")
        metrics_layout = QGridLayout(metrics_frame)
        metrics_layout.setSpacing(15)
        
        # Créer les cartes de métriques
        self.metric_cards = {}
        metrics_config = [
            ("revenue", "Chiffre d'Affaires", "125,430 DA", 8.5, "#4CAF50"),
            ("repairs", "Réparations", "342", 12.3, "#2196F3"),
            ("efficiency", "Efficacité", "87%", -2.1, "#FF9800"),
            ("satisfaction", "Satisfaction", "4.6/5", 5.7, "#9C27B0"),
            ("inventory", "Rotation Stock", "2.3x", 15.2, "#00BCD4"),
            ("margin", "Marge Moyenne", "23.5%", 3.8, "#795548")
        ]
        
        for i, (key, title, value, trend, color) in enumerate(metrics_config):
            card = ModernMetricCard(title, value, trend, color)
            self.metric_cards[key] = card
            row, col = divmod(i, 3)
            metrics_layout.addWidget(card, row, col)
        
        layout.addWidget(metrics_frame)
        
        # Onglets d'analyses détaillées
        self.analysis_tabs = QTabWidget()
        self.analysis_tabs.setObjectName("analysisTabs")
        
        # Onglet Tendances
        trends_tab = self.create_trends_tab()
        self.analysis_tabs.addTab(trends_tab, "📈 Tendances")
        
        # Onglet Comparaisons
        comparison_tab = self.create_comparison_tab()
        self.analysis_tabs.addTab(comparison_tab, "⚖️ Comparaisons")
        
        # Onglet Prédictions
        predictions_tab = self.create_predictions_tab()
        self.analysis_tabs.addTab(predictions_tab, "🔮 Prédictions")
        
        # Onglet Analyses Personnalisées
        custom_tab = self.create_custom_tab()
        self.analysis_tabs.addTab(custom_tab, "🎯 Personnalisé")
        
        layout.addWidget(self.analysis_tabs)
        
        self.apply_styles()
        
    def create_trends_tab(self):
        """Crée l'onglet des tendances"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Splitter pour les graphiques
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Graphique des revenus
        revenue_chart = ModernChartWidget("Évolution du Chiffre d'Affaires")
        revenue_chart.create_sample_chart()
        splitter.addWidget(revenue_chart)
        
        # Graphique des réparations
        repairs_chart = ModernChartWidget("Volume des Réparations")
        repairs_chart.create_sample_chart()
        splitter.addWidget(repairs_chart)
        
        layout.addWidget(splitter)
        
        return tab
        
    def create_comparison_tab(self):
        """Crée l'onglet des comparaisons"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Contrôles de comparaison
        controls_layout = QHBoxLayout()
        
        controls_layout.addWidget(QLabel("Comparer:"))
        
        compare_combo = QComboBox()
        compare_combo.addItems([
            "Mois vs Mois précédent",
            "Trimestre vs Trimestre précédent",
            "Année vs Année précédente",
            "Périodes personnalisées"
        ])
        controls_layout.addWidget(compare_combo)
        
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Graphique de comparaison
        comparison_chart = ModernChartWidget("Analyse Comparative")
        comparison_chart.create_sample_chart()
        layout.addWidget(comparison_chart)
        
        return tab
        
    def create_predictions_tab(self):
        """Crée l'onglet des prédictions"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Informations sur les prédictions
        info_frame = QFrame()
        info_frame.setObjectName("infoFrame")
        info_layout = QVBoxLayout(info_frame)
        
        info_label = QLabel("🔮 Prédictions basées sur l'IA")
        info_label.setObjectName("infoTitle")
        info_layout.addWidget(info_label)
        
        desc_label = QLabel("Analyses prédictives utilisant les données historiques et les tendances actuelles.")
        desc_label.setObjectName("infoDescription")
        info_layout.addWidget(desc_label)
        
        layout.addWidget(info_frame)
        
        # Graphique de prédictions
        predictions_chart = ModernChartWidget("Prédictions - 3 Prochains Mois")
        predictions_chart.create_sample_chart()
        layout.addWidget(predictions_chart)
        
        return tab
        
    def create_custom_tab(self):
        """Crée l'onglet d'analyses personnalisées"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Constructeur de requête
        query_frame = QFrame()
        query_frame.setObjectName("queryFrame")
        query_layout = QGridLayout(query_frame)
        
        query_layout.addWidget(QLabel("Métrique:"), 0, 0)
        metric_combo = QComboBox()
        metric_combo.addItems(["Chiffre d'affaires", "Nombre de réparations", "Satisfaction client"])
        query_layout.addWidget(metric_combo, 0, 1)
        
        query_layout.addWidget(QLabel("Grouper par:"), 0, 2)
        group_combo = QComboBox()
        group_combo.addItems(["Jour", "Semaine", "Mois", "Technicien", "Type d'appareil"])
        query_layout.addWidget(group_combo, 0, 3)
        
        query_layout.addWidget(QLabel("Filtrer:"), 1, 0)
        filter_combo = QComboBox()
        filter_combo.addItems(["Aucun", "Par technicien", "Par type d'appareil", "Par statut"])
        query_layout.addWidget(filter_combo, 1, 1)
        
        generate_button = QPushButton("Générer l'analyse")
        generate_button.setObjectName("actionButton")
        query_layout.addWidget(generate_button, 1, 2, 1, 2)
        
        layout.addWidget(query_frame)
        
        # Zone de résultats
        results_chart = ModernChartWidget("Analyse Personnalisée")
        results_chart.create_sample_chart()
        layout.addWidget(results_chart)
        
        return tab
        
    def apply_styles(self):
        """Applique les styles globaux"""
        self.setStyleSheet("""
            #sectionTitle {
                color: #212121;
                font-size: 24px;
                font-weight: bold;
            }
            #metricsFrame {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 12px;
                padding: 20px;
                margin: 10px 0;
            }
            #analysisTabs {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
            }
            #analysisTabs::pane {
                border: none;
                background: white;
                padding: 20px;
            }
            #analysisTabs QTabBar::tab {
                background: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: 500;
            }
            #analysisTabs QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #2196F3;
                color: #2196F3;
                font-weight: bold;
            }
            #infoFrame {
                background: #E3F2FD;
                border: 1px solid #2196F3;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
            #infoTitle {
                color: #1976D2;
                font-size: 16px;
                font-weight: bold;
            }
            #infoDescription {
                color: #1565C0;
                font-size: 14px;
            }
            #queryFrame {
                background: #FAFAFA;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
    def load_sample_data(self):
        """Charge des données d'exemple"""
        # Simulation du chargement de données
        QTimer.singleShot(500, self.update_metrics)
        
    def update_metrics(self):
        """Met à jour les métriques avec de nouvelles valeurs"""
        # Simulation de mise à jour des données
        import random
        
        for key, card in self.metric_cards.items():
            # Simuler une variation aléatoire
            current_trend = card.trend
            new_trend = current_trend + random.uniform(-2, 2)
            card.update_value(card.value, new_trend)
            
    def refresh_data(self):
        """Rafraîchit toutes les données"""
        # Simulation du rafraîchissement
        self.load_sample_data()
