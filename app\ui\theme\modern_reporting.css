/* Styles modernes pour la vue de reporting */

/* Variables CSS pour la cohérence */
:root {
    --primary-color: #2196F3;
    --secondary-color: #1976D2;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #00BCD4;
    --background-color: #FAFAFA;
    --surface-color: #FFFFFF;
    --text-primary: #212121;
    --text-secondary: #757575;
    --border-color: #E0E0E0;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    --border-radius: 8px;
    --border-radius-large: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Layout principal */
ModernReportingView {
    background-color: var(--background-color);
    font-family: 'Se<PERSON>e UI', <PERSON>hom<PERSON>, Geneva, Verdana, sans-serif;
}

/* Sidebar moderne */
ModernSidebarWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1976D2, stop:1 #1565C0);
    border-right: 1px solid var(--border-color);
    min-width: 250px;
    max-width: 250px;
}

#sidebarTitle {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 15px;
    margin-bottom: 10px;
}

#sidebarTitleLabel {
    color: white;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
}

#sidebarSubtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: normal;
}

#sidebarButton {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    border-radius: 0;
    margin: 2px 10px;
    border-radius: 6px;
}

#sidebarButton:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

#sidebarButton:checked {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: bold;
    border-left: 4px solid white;
}

#sidebarSystem {
    background: rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 15px;
    margin-top: 10px;
}

#sidebarSystemInfo {
    color: rgba(255, 255, 255, 0.7);
    font-size: 11px;
}

/* Barre de filtres moderne */
ModernFilterBar {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 10px 20px;
    box-shadow: var(--shadow-light);
}

#filterFrame {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    margin-right: 10px;
}

#filterFrame QLabel {
    color: var(--text-secondary);
    font-weight: 500;
    margin-right: 8px;
}

#filterFrame QComboBox {
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-weight: 500;
    min-width: 120px;
}

#filterFrame QComboBox::drop-down {
    border: none;
    background: transparent;
}

#filterFrame QComboBox::down-arrow {
    image: url(app/ui/resources/icons/arrow_down.svg);
    width: 12px;
    height: 12px;
}

#filterResetButton {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
}

#filterResetButton:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

#exportButton {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--shadow-light);
}

#exportButton:hover {
    background: var(--secondary-color);
    box-shadow: var(--shadow-medium);
}

/* Zone de contenu */
#contentStack {
    background: var(--background-color);
    border: none;
}

/* Section titre */
#sectionTitle {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

#actionButton {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--shadow-light);
}

#actionButton:hover {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

/* Frame KPI moderne */
#kpiFrame {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-large);
    padding: 20px;
    margin: 15px 0;
    box-shadow: var(--shadow-light);
}

/* KPI moderne */
#modernKPI {
    min-height: 120px;
    max-height: 120px;
    transition: var(--transition);
}

#kpiTitle {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

#kpiValue {
    font-size: 28px;
    font-weight: bold;
    margin: 5px 0;
}

#kpiTrend {
    font-size: 12px;
    font-weight: 500;
}

/* Graphiques */
QWidget[objectName*="chart"] {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 10px 5px;
    box-shadow: var(--shadow-light);
}

/* Tableaux modernes */
QTableWidget {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    gridline-color: var(--border-color);
    selection-background-color: rgba(33, 150, 243, 0.1);
}

QTableWidget::item {
    padding: 12px 8px;
    border-bottom: 1px solid var(--border-color);
}

QTableWidget::item:selected {
    background: rgba(33, 150, 243, 0.1);
    color: var(--text-primary);
}

QHeaderView::section {
    background: var(--background-color);
    color: var(--text-primary);
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid var(--primary-color);
    font-weight: bold;
}

/* Scrollbars modernes */
QScrollBar:vertical {
    background: var(--background-color);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: var(--border-color);
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: var(--text-secondary);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0;
}

/* Animations et transitions */
QWidget {
    transition: var(--transition);
}

/* Responsive design */
@media (max-width: 1200px) {
    ModernSidebarWidget {
        min-width: 200px;
        max-width: 200px;
    }
    
    #sidebarButton {
        padding: 10px 15px;
        font-size: 13px;
    }
}

@media (max-width: 800px) {
    ModernSidebarWidget {
        min-width: 60px;
        max-width: 60px;
    }
    
    #sidebarTitleLabel,
    #sidebarSubtitle {
        display: none;
    }
    
    #sidebarButton {
        padding: 12px 8px;
        text-align: center;
    }
}

/* Thème sombre */
[data-theme="dark"] {
    --background-color: #121212;
    --surface-color: #1E1E1E;
    --text-primary: #FFFFFF;
    --text-secondary: #B3B3B3;
    --border-color: #333333;
}

[data-theme="dark"] ModernSidebarWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1565C0, stop:1 #0D47A1);
}

[data-theme="dark"] #filterFrame {
    background: var(--surface-color);
    border-color: var(--border-color);
}

[data-theme="dark"] #modernKPI {
    background: var(--surface-color);
    border-color: var(--border-color);
}

/* États de chargement */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Effets de survol améliorés */
#modernKPI:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

#actionButton:hover {
    transform: translateY(-1px);
}

#exportButton:hover {
    transform: translateY(-1px);
}
