"""
Tableau de bord unifié avec KPIs personnalisables et vues adaptatives
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QFrame, QScrollArea, QTabWidget, QComboBox, QPushButton,
    QSlider, QCheckBox, QButtonGroup, QRadioButton, QSpinBox,
    QProgressBar, QGroupBox, QSplitter, QListWidget, QListWidgetItem,
    QDialog, QDialogButtonBox, QFormLayout, QColorDialog, QLineEdit,
    QInputDialog, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QSettings
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon, QPainter, QPixmap
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json


class CustomizableKPIWidget(QFrame):
    """Widget KPI personnalisable avec configuration utilisateur"""
    
    value_clicked = pyqtSignal(str)  # Émis quand l'utilisateur clique sur le KPI
    
    def __init__(self, kpi_id: str, title: str, value: str = "0", 
                 color: str = "#2196F3", icon: str = "📊", size: str = "medium"):
        super().__init__()
        self.kpi_id = kpi_id
        self.title = title
        self.value = value
        self.color = color
        self.icon = icon
        self.size = size
        self.trend_data = []
        self.setup_ui()
        
    def setup_ui(self):
        self.setObjectName("customKPI")
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Tailles selon la configuration
        sizes = {
            "small": (150, 80),
            "medium": (200, 120),
            "large": (250, 160)
        }
        width, height = sizes.get(self.size, sizes["medium"])
        self.setFixedSize(width, height)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(5)
        
        # En-tête avec icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setObjectName("kpiIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setObjectName("kpiTitle")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Bouton de configuration
        config_button = QPushButton("⚙️")
        config_button.setObjectName("kpiConfigButton")
        config_button.setFixedSize(20, 20)
        config_button.clicked.connect(self.show_config_dialog)
        header_layout.addWidget(config_button)
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        self.value_label = QLabel(self.value)
        self.value_label.setObjectName("kpiValue")
        layout.addWidget(self.value_label)
        
        # Mini graphique de tendance (optionnel)
        if self.size in ["medium", "large"]:
            self.trend_widget = self.create_mini_chart()
            layout.addWidget(self.trend_widget)
        
        # Informations supplémentaires pour les grandes tailles
        if self.size == "large":
            self.info_label = QLabel("Dernière MAJ: --:--")
            self.info_label.setObjectName("kpiInfo")
            layout.addWidget(self.info_label)
        
        self.apply_styles()
        
    def create_mini_chart(self) -> QWidget:
        """Crée un mini graphique de tendance"""
        chart_widget = QWidget()
        chart_widget.setFixedHeight(30)
        chart_widget.setObjectName("miniChart")
        return chart_widget
        
    def apply_styles(self):
        """Applique les styles personnalisés"""
        font_sizes = {
            "small": {"title": "11px", "value": "16px", "icon": "14px"},
            "medium": {"title": "12px", "value": "20px", "icon": "16px"},
            "large": {"title": "14px", "value": "24px", "icon": "18px"}
        }
        
        fonts = font_sizes.get(self.size, font_sizes["medium"])
        
        self.setStyleSheet(f"""
            #customKPI {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}15, stop:1 {self.color}08);
                border: 2px solid {self.color}30;
                border-radius: 12px;
                margin: 5px;
            }}
            #customKPI:hover {{
                border: 2px solid {self.color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}25, stop:1 {self.color}15);
                transform: translateY(-2px);
            }}
            #kpiIcon {{
                font-size: {fonts['icon']};
                margin-right: 5px;
            }}
            #kpiTitle {{
                color: #757575;
                font-size: {fonts['title']};
                font-weight: 500;
            }}
            #kpiValue {{
                color: {self.color};
                font-size: {fonts['value']};
                font-weight: bold;
                margin: 5px 0;
            }}
            #kpiConfigButton {{
                background: transparent;
                border: none;
                color: #757575;
                font-size: 12px;
            }}
            #kpiConfigButton:hover {{
                color: {self.color};
                background: {self.color}20;
                border-radius: 10px;
            }}
            #kpiInfo {{
                color: #757575;
                font-size: 10px;
            }}
            #miniChart {{
                background: {self.color}10;
                border-radius: 4px;
            }}
        """)
        
    def mousePressEvent(self, event):
        """Gère le clic sur le KPI"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.value_clicked.emit(self.kpi_id)
        super().mousePressEvent(event)
        
    def show_config_dialog(self):
        """Affiche la boîte de dialogue de configuration"""
        dialog = KPIConfigDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            config = dialog.get_config()
            self.apply_config(config)
            
    def apply_config(self, config: Dict):
        """Applique une nouvelle configuration"""
        self.title = config.get("title", self.title)
        self.color = config.get("color", self.color)
        self.icon = config.get("icon", self.icon)
        self.size = config.get("size", self.size)
        
        # Reconstruire l'interface
        self.setup_ui()
        
    def update_value(self, new_value: str, trend_data: List = None):
        """Met à jour la valeur et les données de tendance"""
        self.value = new_value
        self.value_label.setText(new_value)
        
        if trend_data:
            self.trend_data = trend_data
            self.update_mini_chart()
            
        if hasattr(self, 'info_label'):
            self.info_label.setText(f"MAJ: {datetime.now().strftime('%H:%M')}")
            
    def update_mini_chart(self):
        """Met à jour le mini graphique"""
        if hasattr(self, 'trend_widget') and self.trend_data:
            # Dessiner un mini graphique simple
            self.trend_widget.update()
            
    def get_config(self) -> Dict:
        """Retourne la configuration actuelle"""
        return {
            "kpi_id": self.kpi_id,
            "title": self.title,
            "color": self.color,
            "icon": self.icon,
            "size": self.size
        }


class KPIConfigDialog(QDialog):
    """Boîte de dialogue pour configurer un KPI"""
    
    def __init__(self, kpi_widget: CustomizableKPIWidget):
        super().__init__()
        self.kpi_widget = kpi_widget
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Configuration du KPI")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # Formulaire de configuration
        form_layout = QFormLayout()
        
        # Titre
        self.title_edit = QLineEdit(self.kpi_widget.title)
        form_layout.addRow("Titre:", self.title_edit)
        
        # Couleur
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 30)
        self.color_button.setStyleSheet(f"background: {self.kpi_widget.color}; border-radius: 4px;")
        self.color_button.clicked.connect(self.choose_color)
        color_layout.addWidget(self.color_button)
        
        self.color_label = QLabel(self.kpi_widget.color)
        color_layout.addWidget(self.color_label)
        color_layout.addStretch()
        
        form_layout.addRow("Couleur:", color_layout)
        
        # Icône
        self.icon_edit = QLineEdit(self.kpi_widget.icon)
        form_layout.addRow("Icône:", self.icon_edit)
        
        # Taille
        self.size_combo = QComboBox()
        self.size_combo.addItems(["small", "medium", "large"])
        self.size_combo.setCurrentText(self.kpi_widget.size)
        form_layout.addRow("Taille:", self.size_combo)
        
        layout.addLayout(form_layout)
        
        # Boutons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def choose_color(self):
        """Ouvre le sélecteur de couleur"""
        color = QColorDialog.getColor(QColor(self.kpi_widget.color), self)
        if color.isValid():
            color_hex = color.name()
            self.color_button.setStyleSheet(f"background: {color_hex}; border-radius: 4px;")
            self.color_label.setText(color_hex)
            
    def get_config(self) -> Dict:
        """Retourne la configuration saisie"""
        return {
            "title": self.title_edit.text(),
            "color": self.color_label.text(),
            "icon": self.icon_edit.text(),
            "size": self.size_combo.currentText()
        }


class UnifiedDashboardWidget(QWidget):
    """Tableau de bord unifié avec KPIs personnalisables"""
    
    def __init__(self):
        super().__init__()
        self.kpi_widgets = {}
        self.dashboard_config = self.load_dashboard_config()
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec contrôles
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Tableau de Bord Unifié")
        title_label.setObjectName("dashboardTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Contrôles de personnalisation
        customize_button = QPushButton("🎨 Personnaliser")
        customize_button.setObjectName("customizeButton")
        customize_button.clicked.connect(self.show_customization_panel)
        header_layout.addWidget(customize_button)
        
        add_kpi_button = QPushButton("➕ Ajouter KPI")
        add_kpi_button.setObjectName("addKpiButton")
        add_kpi_button.clicked.connect(self.add_new_kpi)
        header_layout.addWidget(add_kpi_button)
        
        layout.addLayout(header_layout)
        
        # Zone de KPIs avec scroll
        self.kpi_scroll = QScrollArea()
        self.kpi_scroll.setWidgetResizable(True)
        self.kpi_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.kpi_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        self.kpi_container = QWidget()
        self.kpi_layout = QGridLayout(self.kpi_container)
        self.kpi_layout.setSpacing(15)
        
        self.kpi_scroll.setWidget(self.kpi_container)
        layout.addWidget(self.kpi_scroll)
        
        # Créer les KPIs par défaut
        self.create_default_kpis()
        
        # Graphiques de synthèse
        charts_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Graphique principal
        main_chart_widget = QWidget()
        main_chart_layout = QVBoxLayout(main_chart_widget)
        
        chart_title = QLabel("Évolution Générale")
        chart_title.setObjectName("chartTitle")
        main_chart_layout.addWidget(chart_title)
        
        self.main_figure = Figure(figsize=(8, 4))
        self.main_canvas = FigureCanvas(self.main_figure)
        main_chart_layout.addWidget(self.main_canvas)
        
        charts_splitter.addWidget(main_chart_widget)
        
        # Graphique secondaire
        secondary_chart_widget = QWidget()
        secondary_chart_layout = QVBoxLayout(secondary_chart_widget)
        
        secondary_title = QLabel("Répartition")
        secondary_title.setObjectName("chartTitle")
        secondary_chart_layout.addWidget(secondary_title)
        
        self.secondary_figure = Figure(figsize=(6, 4))
        self.secondary_canvas = FigureCanvas(self.secondary_figure)
        secondary_chart_layout.addWidget(self.secondary_canvas)
        
        charts_splitter.addWidget(secondary_chart_widget)
        
        layout.addWidget(charts_splitter)
        
        self.apply_styles()
        
    def create_default_kpis(self):
        """Crée les KPIs par défaut"""
        default_kpis = [
            ("revenue", "Chiffre d'Affaires", "125,430 DA", "#4CAF50", "💰", "large"),
            ("repairs", "Réparations", "342", "#2196F3", "🔧", "medium"),
            ("customers", "Clients", "156", "#9C27B0", "👥", "medium"),
            ("efficiency", "Efficacité", "87%", "#FF9800", "⚡", "medium"),
            ("satisfaction", "Satisfaction", "4.6/5", "#00BCD4", "⭐", "medium"),
            ("inventory", "Stock", "2,340", "#795548", "📦", "medium")
        ]
        
        for i, (kpi_id, title, value, color, icon, size) in enumerate(default_kpis):
            kpi_widget = CustomizableKPIWidget(kpi_id, title, value, color, icon, size)
            kpi_widget.value_clicked.connect(self.on_kpi_clicked)
            self.kpi_widgets[kpi_id] = kpi_widget
            
            # Disposition en grille adaptative
            if size == "large":
                self.kpi_layout.addWidget(kpi_widget, 0, 0, 2, 2)  # 2x2
            else:
                row = (i - 1) // 3 + 2  # Commencer après le KPI large
                col = (i - 1) % 3
                self.kpi_layout.addWidget(kpi_widget, row, col)
                
    def load_dashboard_config(self) -> Dict:
        """Charge la configuration du tableau de bord"""
        settings = QSettings("NadjibGSM", "Dashboard")
        config_str = settings.value("dashboard_config", "{}")
        try:
            return json.loads(config_str)
        except:
            return {}
            
    def save_dashboard_config(self):
        """Sauvegarde la configuration du tableau de bord"""
        config = {
            "kpis": {kpi_id: widget.get_config() for kpi_id, widget in self.kpi_widgets.items()}
        }
        settings = QSettings("NadjibGSM", "Dashboard")
        settings.setValue("dashboard_config", json.dumps(config))
        
    def show_customization_panel(self):
        """Affiche le panneau de personnalisation"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Personnalisation", "Panneau de personnalisation en développement")
        
    def add_new_kpi(self):
        """Ajoute un nouveau KPI"""
        from PyQt6.QtWidgets import QInputDialog
        
        title, ok = QInputDialog.getText(self, "Nouveau KPI", "Titre du KPI:")
        if ok and title:
            kpi_id = f"custom_{len(self.kpi_widgets)}"
            kpi_widget = CustomizableKPIWidget(kpi_id, title, "0", "#2196F3", "📊", "medium")
            kpi_widget.value_clicked.connect(self.on_kpi_clicked)
            
            # Ajouter à la grille
            row = len(self.kpi_widgets) // 3 + 2
            col = len(self.kpi_widgets) % 3
            self.kpi_layout.addWidget(kpi_widget, row, col)
            
            self.kpi_widgets[kpi_id] = kpi_widget
            self.save_dashboard_config()
            
    def on_kpi_clicked(self, kpi_id: str):
        """Gère le clic sur un KPI"""
        print(f"KPI cliqué: {kpi_id}")
        # Ici on pourrait ouvrir une vue détaillée du KPI
        
    def load_sample_data(self):
        """Charge des données d'exemple"""
        # Créer des graphiques d'exemple
        self.create_main_chart()
        self.create_secondary_chart()
        
        # Démarrer la mise à jour automatique
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_kpis)
        self.update_timer.start(10000)  # Mise à jour toutes les 10 secondes
        
    def create_main_chart(self):
        """Crée le graphique principal"""
        self.main_figure.clear()
        ax = self.main_figure.add_subplot(111)
        
        # Données d'exemple
        x = np.linspace(0, 30, 30)
        y1 = np.random.normal(100, 15, 30).cumsum()
        y2 = np.random.normal(80, 10, 30).cumsum()
        
        ax.plot(x, y1, label="Revenus", color="#4CAF50", linewidth=2)
        ax.plot(x, y2, label="Coûts", color="#F44336", linewidth=2)
        ax.fill_between(x, y1, y2, alpha=0.3, color="#4CAF50")
        
        ax.set_title("Évolution Financière (30 derniers jours)")
        ax.set_xlabel("Jours")
        ax.set_ylabel("Montant (DA)")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        self.main_figure.tight_layout()
        self.main_canvas.draw()
        
    def create_secondary_chart(self):
        """Crée le graphique secondaire"""
        self.secondary_figure.clear()
        ax = self.secondary_figure.add_subplot(111)
        
        # Graphique en secteurs
        labels = ["Réparations", "Ventes", "Services", "Autres"]
        sizes = [40, 30, 20, 10]
        colors = ["#2196F3", "#4CAF50", "#FF9800", "#9C27B0"]
        
        ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax.set_title("Répartition des Activités")
        
        self.secondary_figure.tight_layout()
        self.secondary_canvas.draw()
        
    def update_kpis(self):
        """Met à jour les valeurs des KPIs"""
        import random
        
        # Simuler des mises à jour
        updates = {
            "revenue": f"{random.randint(120000, 130000):,} DA",
            "repairs": str(random.randint(330, 350)),
            "customers": str(random.randint(150, 160)),
            "efficiency": f"{random.randint(85, 90)}%",
            "satisfaction": f"{random.uniform(4.5, 4.8):.1f}/5",
            "inventory": f"{random.randint(2300, 2400):,}"
        }
        
        for kpi_id, new_value in updates.items():
            if kpi_id in self.kpi_widgets:
                self.kpi_widgets[kpi_id].update_value(new_value)
                
    def apply_styles(self):
        """Applique les styles globaux"""
        self.setStyleSheet("""
            #dashboardTitle {
                color: #212121;
                font-size: 24px;
                font-weight: bold;
            }
            #customizeButton, #addKpiButton {
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                margin-left: 10px;
            }
            #customizeButton:hover, #addKpiButton:hover {
                background: #1976D2;
            }
            #chartTitle {
                color: #212121;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        
    def apply_filters(self, filters: Dict):
        """Applique les filtres au tableau de bord"""
        print(f"Application des filtres au tableau de bord: {filters}")
        # Logique de filtrage des données
        
    def get_data(self) -> Dict:
        """Retourne les données du tableau de bord"""
        return {
            "kpis": {kpi_id: widget.value for kpi_id, widget in self.kpi_widgets.items()},
            "last_update": datetime.now().isoformat()
        }
