#!/usr/bin/env python3
"""
Script de test pour la vue de rapports modernisée
Valide toutes les fonctionnalités et améliorations apportées
"""

import sys
import os
import time
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtTest import QTest

# Ajouter le chemin vers les modules de l'application
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.ui.views.reporting.modern_reporting_view import ModernReportingView
    from app.ui.views.reporting.integration_guide import ModernReportingIntegration
    IMPORTS_OK = True
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    IMPORTS_OK = False


class TestModernReporting:
    """Classe de test pour la vue de rapports modernisée"""
    
    def __init__(self):
        self.app = None
        self.window = None
        self.modern_view = None
        self.test_results = {}
        
    def setup_test_environment(self):
        """Configure l'environnement de test"""
        print("🔧 Configuration de l'environnement de test...")
        
        # Créer l'application Qt
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
            
        # Configuration de l'application
        self.app.setApplicationName("Test Rapports Modernisés")
        self.app.setApplicationVersion("2.0-test")
        
        # Créer la fenêtre de test
        self.window = QMainWindow()
        self.window.setWindowTitle("Test - Rapports Modernisés")
        self.window.setGeometry(100, 100, 1400, 900)
        
        print("✅ Environnement de test configuré")
        
    def test_imports(self):
        """Test des imports et dépendances"""
        print("\n📦 Test des imports...")
        
        if not IMPORTS_OK:
            self.test_results["imports"] = False
            print("❌ Échec des imports")
            return False
            
        try:
            # Test des imports spécifiques
            from app.ui.views.reporting.widgets.modern_analytics_widget import ModernAnalyticsWidget
            from app.ui.views.reporting.widgets.modern_reports_widget import ModernReportsWidget
            from app.ui.views.reporting.widgets.modern_operations_widget import ModernOperationsWidget
            from app.ui.views.reporting.widgets.unified_dashboard_widget import UnifiedDashboardWidget
            
            self.test_results["imports"] = True
            print("✅ Tous les imports sont OK")
            return True
            
        except ImportError as e:
            self.test_results["imports"] = False
            print(f"❌ Erreur d'import: {e}")
            return False
            
    def test_view_creation(self):
        """Test de création de la vue modernisée"""
        print("\n🏗️ Test de création de la vue...")
        
        try:
            # Créer la vue modernisée
            self.modern_view = ModernReportingView()
            
            # Vérifier que la vue est créée
            if self.modern_view is None:
                raise Exception("La vue n'a pas pu être créée")
                
            # Vérifier les composants principaux
            if not hasattr(self.modern_view, 'sidebar'):
                raise Exception("Sidebar manquante")
                
            if not hasattr(self.modern_view, 'filter_bar'):
                raise Exception("Barre de filtres manquante")
                
            if not hasattr(self.modern_view, 'content_stack'):
                raise Exception("Stack de contenu manquant")
                
            self.test_results["view_creation"] = True
            print("✅ Vue créée avec succès")
            return True
            
        except Exception as e:
            self.test_results["view_creation"] = False
            print(f"❌ Erreur de création: {e}")
            return False
            
    def test_sections(self):
        """Test des sections de la vue"""
        print("\n📑 Test des sections...")
        
        if not self.modern_view:
            self.test_results["sections"] = False
            print("❌ Vue non disponible pour le test")
            return False
            
        try:
            # Vérifier que toutes les sections existent
            expected_sections = ["dashboard", "analytics", "reports", "financial", "operations", "alerts"]
            
            for section_id in expected_sections:
                if section_id not in self.modern_view.sections:
                    raise Exception(f"Section manquante: {section_id}")
                    
                section_widget = self.modern_view.sections[section_id]
                if section_widget is None:
                    raise Exception(f"Widget de section None: {section_id}")
                    
            self.test_results["sections"] = True
            print(f"✅ Toutes les sections sont présentes ({len(expected_sections)})")
            return True
            
        except Exception as e:
            self.test_results["sections"] = False
            print(f"❌ Erreur des sections: {e}")
            return False
            
    def test_navigation(self):
        """Test de la navigation entre sections"""
        print("\n🧭 Test de navigation...")
        
        if not self.modern_view:
            self.test_results["navigation"] = False
            print("❌ Vue non disponible pour le test")
            return False
            
        try:
            # Tester le changement de section
            original_section = self.modern_view.current_section
            
            # Changer vers une autre section
            test_section = "analytics" if original_section != "analytics" else "reports"
            self.modern_view.change_section(test_section)
            
            # Attendre un peu pour l'animation
            QTest.qWait(500)
            
            # Vérifier le changement
            if self.modern_view.current_section != test_section:
                raise Exception(f"Changement de section échoué: {self.modern_view.current_section} != {test_section}")
                
            # Revenir à la section originale
            self.modern_view.change_section(original_section)
            QTest.qWait(500)
            
            self.test_results["navigation"] = True
            print("✅ Navigation fonctionnelle")
            return True
            
        except Exception as e:
            self.test_results["navigation"] = False
            print(f"❌ Erreur de navigation: {e}")
            return False
            
    def test_filters(self):
        """Test du système de filtres"""
        print("\n🔍 Test des filtres...")
        
        if not self.modern_view:
            self.test_results["filters"] = False
            print("❌ Vue non disponible pour le test")
            return False
            
        try:
            # Tester l'application de filtres
            test_filters = {
                "period": "Ce mois",
                "category": "Réparations",
                "status": "Actif"
            }
            
            # Appliquer les filtres
            self.modern_view.apply_filters(test_filters)
            
            # Vérifier que les filtres sont appliqués
            current_filters = self.modern_view.filter_bar.get_current_filters()
            
            self.test_results["filters"] = True
            print("✅ Système de filtres fonctionnel")
            return True
            
        except Exception as e:
            self.test_results["filters"] = False
            print(f"❌ Erreur des filtres: {e}")
            return False
            
    def test_dashboard_kpis(self):
        """Test des KPIs du tableau de bord"""
        print("\n📊 Test des KPIs...")
        
        if not self.modern_view:
            self.test_results["kpis"] = False
            print("❌ Vue non disponible pour le test")
            return False
            
        try:
            # Accéder au tableau de bord
            dashboard = self.modern_view.sections.get("dashboard")
            if not dashboard:
                raise Exception("Tableau de bord non trouvé")
                
            # Vérifier les KPIs
            if hasattr(dashboard, 'kpi_widgets'):
                kpi_count = len(dashboard.kpi_widgets)
                if kpi_count == 0:
                    raise Exception("Aucun KPI trouvé")
                    
                print(f"   📈 {kpi_count} KPIs détectés")
                
                # Tester la mise à jour d'un KPI
                first_kpi = next(iter(dashboard.kpi_widgets.values()))
                original_value = first_kpi.value
                first_kpi.update_value("TEST_VALUE")
                
                if first_kpi.value != "TEST_VALUE":
                    raise Exception("Mise à jour KPI échouée")
                    
                # Restaurer la valeur originale
                first_kpi.update_value(original_value)
                
            self.test_results["kpis"] = True
            print("✅ KPIs fonctionnels")
            return True
            
        except Exception as e:
            self.test_results["kpis"] = False
            print(f"❌ Erreur des KPIs: {e}")
            return False
            
    def test_styles(self):
        """Test du chargement des styles"""
        print("\n🎨 Test des styles...")
        
        try:
            # Charger les styles modernes
            ModernReportingIntegration.load_modern_styles(self.app)
            
            # Vérifier que les styles sont appliqués
            stylesheet = self.app.styleSheet()
            
            if not stylesheet or len(stylesheet) < 100:
                print("⚠️ Styles non chargés ou incomplets")
                self.test_results["styles"] = False
                return False
                
            self.test_results["styles"] = True
            print("✅ Styles chargés avec succès")
            return True
            
        except Exception as e:
            self.test_results["styles"] = False
            print(f"❌ Erreur des styles: {e}")
            return False
            
    def test_performance(self):
        """Test de performance"""
        print("\n⚡ Test de performance...")
        
        if not self.modern_view:
            self.test_results["performance"] = False
            print("❌ Vue non disponible pour le test")
            return False
            
        try:
            # Test de temps de chargement
            start_time = time.time()
            self.modern_view.init_data()
            
            # Attendre la fin du chargement
            QTest.qWait(2000)
            
            load_time = time.time() - start_time
            
            # Test de temps de navigation
            start_time = time.time()
            self.modern_view.change_section("analytics")
            QTest.qWait(500)
            nav_time = time.time() - start_time
            
            print(f"   ⏱️ Temps de chargement: {load_time:.2f}s")
            print(f"   🧭 Temps de navigation: {nav_time:.2f}s")
            
            # Critères de performance
            if load_time > 5.0:
                print("⚠️ Temps de chargement lent")
                
            if nav_time > 1.0:
                print("⚠️ Navigation lente")
                
            self.test_results["performance"] = True
            print("✅ Tests de performance terminés")
            return True
            
        except Exception as e:
            self.test_results["performance"] = False
            print(f"❌ Erreur de performance: {e}")
            return False
            
    def run_all_tests(self):
        """Exécute tous les tests"""
        print("🚀 Démarrage des tests de la vue modernisée...\n")
        
        # Configuration
        self.setup_test_environment()
        
        # Tests
        tests = [
            ("Imports", self.test_imports),
            ("Création de vue", self.test_view_creation),
            ("Sections", self.test_sections),
            ("Navigation", self.test_navigation),
            ("Filtres", self.test_filters),
            ("KPIs", self.test_dashboard_kpis),
            ("Styles", self.test_styles),
            ("Performance", self.test_performance)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if test_func():
                passed += 1
                
        # Résultats
        print(f"\n📋 RÉSULTATS DES TESTS")
        print(f"{'='*50}")
        print(f"Tests réussis: {passed}/{total}")
        print(f"Taux de réussite: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 TOUS LES TESTS SONT PASSÉS!")
            return True
        else:
            print("⚠️ Certains tests ont échoué")
            return False
            
    def show_demo(self):
        """Affiche une démonstration de la vue"""
        if self.modern_view and self.window:
            # Ajouter la vue à la fenêtre
            central_widget = QWidget()
            self.window.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(self.modern_view)
            
            # Afficher la fenêtre
            self.window.show()
            
            print("\n🎭 Démonstration lancée!")
            print("   - Testez la navigation dans la sidebar")
            print("   - Essayez les filtres en haut")
            print("   - Explorez les différentes sections")
            print("   - Fermez la fenêtre pour terminer")
            
            return self.app.exec()
        
        return 0


def main():
    """Fonction principale"""
    print("🔬 TESTS DE LA VUE RAPPORTS MODERNISÉE")
    print("="*50)
    
    # Créer l'instance de test
    tester = TestModernReporting()
    
    # Exécuter les tests
    success = tester.run_all_tests()
    
    if success:
        # Proposer la démonstration
        print("\n🎭 Voulez-vous voir une démonstration? (Fermez la fenêtre pour terminer)")
        return tester.show_demo()
    else:
        print("\n❌ Des erreurs ont été détectées. Vérifiez les logs ci-dessus.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
