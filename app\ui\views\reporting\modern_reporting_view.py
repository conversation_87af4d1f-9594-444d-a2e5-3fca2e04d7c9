"""
Vue modernisée pour les rapports et statistiques
Nouvelle architecture modulaire avec interface responsive et navigation améliorée
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QComboBox, QPushButton, QFrame, QScrollArea, QGridLayout,
    QSplitter, QGroupBox, QStackedWidget, QToolBar, QAction,
    QButtonGroup, QRadioButton, QCheckBox, QSlider, QSpinBox,
    QProgressBar, QTextEdit, QListWidget, QTreeWidget, QTreeWidgetItem
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QIcon, QFont, QPalette, QColor
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from .widgets.chart_widget import (
    <PERSON>air<PERSON>rend<PERSON>hart, InventoryTrendChart, RepairStatusChart,
    TopEquipmentChart
)
from .widgets.kpi_widget import KPIWidget
from .widgets.report_table import ReportTable
from .widgets.technician_report_widget import TechnicianReportWidget
from .widgets.sales_report_widget import SalesReportWidget
from .widgets.purchases_report_widget import PurchasesReportWidget
from .widgets.inventory_report_widget import InventoryReportWidget
from .widgets.margin_analysis_widget import MarginAnalysisWidget
from .widgets.period_comparison_widget import PeriodComparisonWidget
from .widgets.executive_dashboard_widget import ExecutiveDashboardWidget
from .widgets.alerts_widget import AlertsWidget
from .widgets.modern_analytics_widget import ModernAnalyticsWidget
from .widgets.modern_reports_widget import ModernReportsWidget
from .widgets.modern_operations_widget import ModernOperationsWidget
from .widgets.unified_dashboard_widget import UnifiedDashboardWidget
from .financial_reporting_view import FinancialReportingView
from ...components.custom_widgets import LoadingOverlay
from app.core.services.reporting_service import ReportingService


class ModernSidebarWidget(QWidget):
    """Widget de navigation latérale moderne"""
    
    section_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Logo/Titre
        title_frame = QFrame()
        title_frame.setObjectName("sidebarTitle")
        title_layout = QVBoxLayout(title_frame)
        
        title_label = QLabel("📊 Rapports")
        title_label.setObjectName("sidebarTitleLabel")
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Analyses & Statistiques")
        subtitle_label.setObjectName("sidebarSubtitle")
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
        
        # Sections de navigation
        self.nav_sections = {
            "dashboard": {"name": "🏠 Tableau de Bord", "icon": "dashboard"},
            "analytics": {"name": "📈 Analyses", "icon": "analytics"},
            "reports": {"name": "📋 Rapports", "icon": "reports"},
            "financial": {"name": "💰 Finances", "icon": "financial"},
            "operations": {"name": "⚙️ Opérations", "icon": "operations"},
            "alerts": {"name": "🔔 Alertes", "icon": "alerts"}
        }
        
        self.nav_buttons = {}
        self.button_group = QButtonGroup(self)
        
        for section_id, section_info in self.nav_sections.items():
            button = QPushButton(section_info["name"])
            button.setObjectName("sidebarButton")
            button.setCheckable(True)
            button.clicked.connect(lambda checked, sid=section_id: self.section_changed.emit(sid))
            
            self.nav_buttons[section_id] = button
            self.button_group.addButton(button)
            layout.addWidget(button)
        
        # Sélectionner le premier par défaut
        self.nav_buttons["dashboard"].setChecked(True)
        
        layout.addStretch()
        
        # Informations système
        system_frame = QFrame()
        system_frame.setObjectName("sidebarSystem")
        system_layout = QVBoxLayout(system_frame)
        
        self.last_update_label = QLabel("Dernière MAJ: --:--")
        self.last_update_label.setObjectName("sidebarSystemInfo")
        system_layout.addWidget(self.last_update_label)
        
        layout.addWidget(system_frame)


class ModernFilterBar(QWidget):
    """Barre de filtres moderne et contextuelle"""

    filters_changed = pyqtSignal(dict)
    export_requested = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.current_filters = {}
        self.setup_ui()

    def setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Filtres de période
        period_frame = QFrame()
        period_frame.setObjectName("filterFrame")
        period_layout = QHBoxLayout(period_frame)

        period_layout.addWidget(QLabel("Période:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "Aujourd'hui", "Cette semaine", "Ce mois", "Ce trimestre",
            "Cette année", "30 derniers jours", "90 derniers jours", "Personnalisé"
        ])
        self.period_combo.currentTextChanged.connect(self._emit_filters)
        period_layout.addWidget(self.period_combo)

        layout.addWidget(period_frame)

        # Filtres de catégorie
        category_frame = QFrame()
        category_frame.setObjectName("filterFrame")
        category_layout = QHBoxLayout(category_frame)

        category_layout.addWidget(QLabel("Catégorie:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(["Toutes", "Réparations", "Ventes", "Achats", "Inventaire"])
        self.category_combo.currentTextChanged.connect(self._emit_filters)
        category_layout.addWidget(self.category_combo)

        layout.addWidget(category_frame)

        # Filtres de statut
        status_frame = QFrame()
        status_frame.setObjectName("filterFrame")
        status_layout = QHBoxLayout(status_frame)

        status_layout.addWidget(QLabel("Statut:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["Tous", "Actif", "Terminé", "En attente", "Annulé"])
        self.status_combo.currentTextChanged.connect(self._emit_filters)
        status_layout.addWidget(self.status_combo)

        layout.addWidget(status_frame)

        # Bouton de filtres avancés
        advanced_button = QPushButton("🔍 Avancé")
        advanced_button.setObjectName("advancedFilterButton")
        advanced_button.clicked.connect(self._show_advanced_filters)
        layout.addWidget(advanced_button)

        # Bouton de réinitialisation
        reset_button = QPushButton("🔄 Reset")
        reset_button.setObjectName("filterResetButton")
        reset_button.clicked.connect(self._reset_filters)
        layout.addWidget(reset_button)

        layout.addStretch()

        # Indicateur de filtres actifs
        self.filter_indicator = QLabel("")
        self.filter_indicator.setObjectName("filterIndicator")
        layout.addWidget(self.filter_indicator)

        # Bouton d'export
        export_button = QPushButton("📤 Exporter")
        export_button.setObjectName("exportButton")
        export_button.clicked.connect(self._request_export)
        layout.addWidget(export_button)

    def _emit_filters(self):
        filters = {
            "period": self.period_combo.currentText(),
            "category": self.category_combo.currentText(),
            "status": self.status_combo.currentText()
        }
        self.current_filters = filters
        self._update_filter_indicator()
        self.filters_changed.emit(filters)

    def _reset_filters(self):
        self.period_combo.setCurrentIndex(0)
        self.category_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.current_filters = {}
        self._update_filter_indicator()

    def _show_advanced_filters(self):
        """Affiche la boîte de dialogue des filtres avancés"""
        # Placeholder pour les filtres avancés
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Filtres Avancés", "Fonctionnalité en développement")

    def _request_export(self):
        """Demande l'export avec les filtres actuels"""
        export_config = {
            "filters": self.current_filters,
            "format": "pdf",  # Par défaut
            "timestamp": datetime.now().isoformat()
        }
        self.export_requested.emit(export_config)

    def _update_filter_indicator(self):
        """Met à jour l'indicateur de filtres actifs"""
        active_filters = [v for v in self.current_filters.values() if v not in ["Toutes", "Tous", "Aujourd'hui"]]
        if active_filters:
            self.filter_indicator.setText(f"🔍 {len(active_filters)} filtre(s)")
            self.filter_indicator.setStyleSheet("color: #2196F3; font-weight: bold;")
        else:
            self.filter_indicator.setText("")

    def get_current_filters(self) -> Dict:
        """Retourne les filtres actuellement actifs"""
        return self.current_filters.copy()


class ModernDashboardSection(QWidget):
    """Section tableau de bord modernisée"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # En-tête de section
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Tableau de Bord Principal")
        title_label.setObjectName("sectionTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Boutons d'action rapide
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("actionButton")
        header_layout.addWidget(refresh_button)
        
        layout.addLayout(header_layout)
        
        # Grille de KPIs
        kpi_frame = QFrame()
        kpi_frame.setObjectName("kpiFrame")
        kpi_layout = QGridLayout(kpi_frame)
        
        # KPIs principaux avec design moderne
        self.kpi_widgets = {}
        kpi_configs = [
            ("repairs_active", "Réparations Actives", "0", "#2196F3"),
            ("revenue_month", "CA du Mois", "0 DA", "#4CAF50"),
            ("inventory_alerts", "Alertes Stock", "0", "#FF9800"),
            ("satisfaction", "Satisfaction", "0%", "#9C27B0"),
            ("efficiency", "Efficacité", "0%", "#00BCD4"),
            ("profit_margin", "Marge Bénéficiaire", "0%", "#795548")
        ]
        
        for i, (key, title, value, color) in enumerate(kpi_configs):
            kpi_widget = self._create_modern_kpi(title, value, color)
            self.kpi_widgets[key] = kpi_widget
            row, col = divmod(i, 3)
            kpi_layout.addWidget(kpi_widget, row, col)
        
        layout.addWidget(kpi_frame)
        
        # Graphiques principaux
        charts_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Graphique des tendances
        trend_widget = QWidget()
        trend_layout = QVBoxLayout(trend_widget)
        trend_layout.addWidget(QLabel("Tendances des Réparations"))
        self.trend_chart = RepairTrendChart()
        trend_layout.addWidget(self.trend_chart)
        charts_splitter.addWidget(trend_widget)
        
        # Graphique des statuts
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.addWidget(QLabel("Répartition des Statuts"))
        self.status_chart = RepairStatusChart()
        status_layout.addWidget(self.status_chart)
        charts_splitter.addWidget(status_widget)
        
        layout.addWidget(charts_splitter)
        
    def _create_modern_kpi(self, title: str, value: str, color: str) -> QWidget:
        """Crée un widget KPI moderne"""
        widget = QFrame()
        widget.setObjectName("modernKPI")
        widget.setStyleSheet(f"""
            #modernKPI {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}20, stop:1 {color}10);
                border: 2px solid {color}40;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }}
            #modernKPI:hover {{
                border: 2px solid {color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}30, stop:1 {color}20);
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(5)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("kpiTitle")
        layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("kpiValue")
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")
        layout.addWidget(value_label)
        
        # Indicateur de tendance (placeholder)
        trend_label = QLabel("↗ +5.2%")
        trend_label.setObjectName("kpiTrend")
        trend_label.setStyleSheet("color: #4CAF50; font-size: 12px;")
        layout.addWidget(trend_label)
        
        return widget


class ModernReportingView(QWidget):
    """Vue modernisée pour les rapports et statistiques"""
    
    def __init__(self):
        super().__init__()
        self.service = ReportingService()
        self.current_section = "dashboard"
        self.setup_ui()
        self.setup_animations()
        
        # Timer pour le rafraîchissement automatique
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data)
        
        # Démarrer le chargement initial
        QTimer.singleShot(100, self.init_data)
        
    def setup_ui(self):
        """Configure l'interface utilisateur moderne"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar de navigation
        self.sidebar = ModernSidebarWidget()
        self.sidebar.section_changed.connect(self.change_section)
        main_layout.addWidget(self.sidebar)
        
        # Zone de contenu principal
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # Barre de filtres
        self.filter_bar = ModernFilterBar()
        self.filter_bar.filters_changed.connect(self.apply_filters)
        self.filter_bar.export_requested.connect(self.handle_export_request)
        content_layout.addWidget(self.filter_bar)
        
        # Zone de contenu avec stack
        self.content_stack = QStackedWidget()
        self.content_stack.setObjectName("contentStack")
        
        # Créer les sections
        self.sections = {}
        self._create_sections()
        
        content_layout.addWidget(self.content_stack)
        
        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(content_widget)
        self.loading_overlay.hide()
        
        main_layout.addWidget(content_widget, 1)
        
    def _create_sections(self):
        """Crée toutes les sections de contenu"""
        # Section Tableau de Bord (unifié)
        self.sections["dashboard"] = UnifiedDashboardWidget()
        self.content_stack.addWidget(self.sections["dashboard"])
        
        # Section Analyses (widget moderne)
        self.sections["analytics"] = ModernAnalyticsWidget()
        self.content_stack.addWidget(self.sections["analytics"])
        
        # Section Rapports (widget moderne)
        self.sections["reports"] = ModernReportsWidget()
        self.content_stack.addWidget(self.sections["reports"])
        
        # Section Finances (utilise la vue existante)
        self.sections["financial"] = FinancialReportingView()
        self.content_stack.addWidget(self.sections["financial"])
        
        # Section Opérations (widget moderne)
        self.sections["operations"] = ModernOperationsWidget()
        self.content_stack.addWidget(self.sections["operations"])
        
        # Section Alertes
        self.sections["alerts"] = AlertsWidget()
        self.content_stack.addWidget(self.sections["alerts"])
        
    def setup_animations(self):
        """Configure les animations pour les transitions"""
        self.fade_animation = QPropertyAnimation(self.content_stack, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def change_section(self, section_id: str):
        """Change la section active avec animation"""
        if section_id == self.current_section:
            return
            
        self.current_section = section_id
        
        # Animation de transition
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(lambda: self._complete_section_change(section_id))
        self.fade_animation.start()
        
    def _complete_section_change(self, section_id: str):
        """Complète le changement de section"""
        if section_id in self.sections:
            self.content_stack.setCurrentWidget(self.sections[section_id])
            
        # Animation de retour
        self.fade_animation.finished.disconnect()
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
        
    def apply_filters(self, filters: Dict):
        """Applique les filtres sélectionnés"""
        print(f"Filtres appliqués: {filters}")

        # Appliquer les filtres à la section active
        current_section = self.sections.get(self.current_section)
        if current_section and hasattr(current_section, 'apply_filters'):
            current_section.apply_filters(filters)

        # Mettre à jour l'affichage
        self._update_section_with_filters(filters)

    def handle_export_request(self, export_config: Dict):
        """Gère les demandes d'export"""
        from PyQt6.QtWidgets import QMessageBox, QFileDialog

        # Demander le format d'export
        formats = ["PDF", "Excel", "CSV", "JSON"]
        format_choice, ok = QMessageBox.question(
            self, "Format d'Export",
            "Choisissez le format d'export:",
            QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Cancel
        )

        if ok:
            # Demander l'emplacement de sauvegarde
            filename, _ = QFileDialog.getSaveFileName(
                self, "Exporter les données",
                f"export_rapports_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "Fichiers PDF (*.pdf);;Fichiers Excel (*.xlsx);;Fichiers CSV (*.csv)"
            )

            if filename:
                self._perform_export(filename, export_config)

    def _perform_export(self, filename: str, config: Dict):
        """Effectue l'export des données"""
        try:
            # Simulation de l'export
            QTimer.singleShot(1000, lambda: self._export_completed(filename))
            print(f"Export en cours vers: {filename}")

        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'Export", f"Erreur lors de l'export: {str(e)}")

    def _export_completed(self, filename: str):
        """Callback quand l'export est terminé"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "Export Terminé", f"Export réussi vers:\n{filename}")

    def _update_section_with_filters(self, filters: Dict):
        """Met à jour la section active avec les filtres"""
        # Logique de mise à jour spécifique à chaque section
        if self.current_section == "dashboard":
            self._update_dashboard_filters(filters)
        elif self.current_section == "analytics":
            self._update_analytics_filters(filters)
        # Ajouter d'autres sections selon les besoins

    def _update_dashboard_filters(self, filters: Dict):
        """Met à jour le tableau de bord avec les filtres"""
        dashboard = self.sections.get("dashboard")
        if dashboard:
            # Mettre à jour les KPIs selon les filtres
            period = filters.get("period", "Ce mois")
            category = filters.get("category", "Toutes")

            # Simulation de mise à jour des données
            print(f"Mise à jour du tableau de bord: {period}, {category}")

    def _update_analytics_filters(self, filters: Dict):
        """Met à jour les analyses avec les filtres"""
        analytics = self.sections.get("analytics")
        if analytics and hasattr(analytics, 'apply_filters'):
            analytics.apply_filters(filters)

    def init_data(self):
        """Initialise les données"""
        self.loading_overlay.show()
        # Simulation du chargement
        QTimer.singleShot(1000, self._data_loaded)

    def _data_loaded(self):
        """Callback quand les données sont chargées"""
        self.loading_overlay.hide()
        self.sidebar.last_update_label.setText(f"Dernière MAJ: {datetime.now().strftime('%H:%M')}")

    def refresh_data(self):
        """Rafraîchit les données"""
        self.init_data()

    def get_current_section_data(self) -> Dict:
        """Retourne les données de la section active"""
        current_section = self.sections.get(self.current_section)
        if current_section and hasattr(current_section, 'get_data'):
            return current_section.get_data()
        return {}

    def set_auto_refresh(self, enabled: bool, interval: int = 30):
        """Configure le rafraîchissement automatique"""
        if enabled:
            self.refresh_timer.start(interval * 1000)
        else:
            self.refresh_timer.stop()
